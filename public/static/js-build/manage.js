$(function(){
    // 搜索功能
    $('.search-input').on('keypress', function(e) {
        // 监听回车键(keyCode 13)
        if(e.keyCode == 13) {
            e.preventDefault(); // 阻止默认的回车换行
            $('#search-form').submit(); // 提交表单
        }
    });

    // 列表数据滚动加载
    let currentPage = 1;
    let isLoading = false;
    let hasMore = true;
    let loadingTimer = null; // 添加防抖定时器
    // 获取隐藏域中的值，使用name属性获取
    const status = $('input[name="status"]').val();
    const keyword = $('input[name="keyword"]').val();
    const $memberListContainer = $('#user-list-container');
    const $loadingElement = $('#loading');
    const $noMoreElement = $('#no-more');

    // 显示加载状态
    function showLoading() {
        isLoading = true;
        $loadingElement.removeClass('hidden');
        $noMoreElement.addClass('hidden');
    }
    
    // 隐藏加载状态
    function hideLoading() {
        isLoading = false;
        $loadingElement.addClass('hidden');
    }
    
    // 显示没有更多数据
    function showNoMore() {
        $noMoreElement.removeClass('hidden');
    }
    
    // 加载更多数据
    function loadMoreData() {
        if (isLoading || !hasMore) return;
        
        showLoading();
        currentPage++;
        
        $.ajax({
            url: '/Manage/loadMore',
            type: 'GET',
            data: {
                status: status,
                keyword: keyword,
                page: currentPage
            },
            dataType: 'json',
            success: function(data) {
                hideLoading();
                
                if (data.code === 200) {
                    hasMore = data.has_more;
                    
                    if (!hasMore) {
                        showNoMore();
                    }
                    
                    if (data.data && data.data.length > 0) {
                        // 将新数据添加到列表中
                        $.each(data.data, function(index, item) {
                            $memberListContainer.append(createMemberItem(item));
                        });
                    }
                } else {
                    console.error('加载失败:', data.msg);
                    showNoMore();
                }
            },
            error: function(xhr, status, error) {
                console.error('请求错误:', error);
                hideLoading();
                showNoMore();
            }
        });
    }

    // 创建会员卡片元素
    function createMemberItem(item) {
        // 解码base64编码的昵称
        const nickname = decodeBase64(item.nickname);
        
        let statusButtons = '';
        if (item.status == 1) {
            statusButtons = `
                <button class="px-4 py-1.5 bg-green-500 text-white text-sm rounded-button">
                    <a href="/Manage/check?id=${item.tube_id}&status=9">通过</a>
                </button>
                <button class="px-4 py-1.5 bg-red-500 text-white text-sm rounded-button">
                    <a href="/Manage/check?id=${item.tube_id}&status=5">拒绝</a>
                </button>
            `;
        }
        
        return $('<div>', {
            'class': 'bg-white rounded-lg shadow-sm p-4 mb-4',
            'html': `
                <div class="flex">
                    <div class="mr-3">
                        <img src="${item.avatar}" alt="会员头像" class="w-16 h-16 object-cover rounded-lg">
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center">
                            <h3 class="text-base font-medium">${nickname}</h3>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">${item.tube_id}</p>
                        <div class="grid grid-cols-4 mt-3">
                            <div>
                                <p class="text-xs text-gray-500">今日观看</p>
                                <p class="text-sm text-primary mt-1">0 人</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">今日完播</p>
                                <p class="text-sm text-primary mt-1">0 人</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">完播率</p>
                                <p class="text-sm text-primary mt-1">0 %</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">红包金额</p>
                                <p class="text-sm text-primary mt-1">0.00 元</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end mt-4 space-x-2">
                    ${statusButtons}
                </div>
            `
        });
    }
    
    // 解码base64
    function decodeBase64(str) {
        try {
            return decodeURIComponent(escape(window.atob(str)));
        } catch (e) {
            return str;
        }
    }
    
    // 监听滚动事件
    let lastScrollTop = 0;
    $(window).on('scroll', function() {
        const currentScrollTop = $(window).scrollTop();
        // 只在向下滚动且接近底部时加载更多数据
        if (currentScrollTop > lastScrollTop && currentScrollTop + $(window).height() >= $(document).height() - 200) {
            // 使用防抖机制，避免短时间内多次触发
            clearTimeout(loadingTimer);
            loadingTimer = setTimeout(function() {
                loadMoreData();
            }, 200);
        }
        lastScrollTop = currentScrollTop;
    });
    
    // 初始化
    if ($('.user-row').length > 0) {
        hasMore = true;
    } else {
        hasMore = false;
        showNoMore();
    }

    // 编辑用户信息模态窗口相关逻辑

    // 打开编辑模态窗口
    $(document).on('click', '.edit-user-btn', function() {
        const userId = $(this).data('user-id');
        const realName = decodeBase64($(this).data('real-name'));
        const mobile = $(this).data('mobile');

        // 填充表单数据
        $('#editUserId').val(userId);
        $('#editUserName').val(realName);
        $('#editUserPhone').val(mobile);

        // 显示模态窗口
        $('#editUserModal').removeClass('hidden');
    });

    // 关闭编辑模态窗口
    $('#closeEditModal, #cancelEditUser').on('click', function() {
        $('#editUserModal').addClass('hidden');
    });

    // 提交编辑表单
    $('#editUserForm').on('submit', function(e) {
        e.preventDefault();

        const userId = $('#editUserId').val();
        const realName = $('#editUserName').val();
        const mobile = $('#editUserPhone').val();

        // 表单验证
        if (!realName.trim()) {
            _alert('请输入会员姓名');
            return;
        }

        if (mobile && !/^1[3-9]\d{9}$/.test(mobile)) {
            _alert('请输入正确的手机号码');
            return;
        }

        // 发送AJAX请求
        $.ajax({
            url: '/Manage/updateTubeInfo',
            type: 'POST',
            data: {
                user_id: userId,
                real_name: realName,
                mobile: mobile
            },
            dataType: 'json',
            success: function(res) {
                if (res.code === 200) {
                    _alert('更新成功');
                    $('#editUserModal').addClass('hidden');

                    // 刷新页面
                    window.location.reload();
                } else {
                    _alert(res.msg || '更新失败');
                }
            },
            error: function() {
                _alert('网络错误，请稍后重试');
            }
        });
    });
});

// 设置模态窗口相关逻辑
$(document).ready(function() {
    // 打开设置模态窗
    $('.agency_set').on('click', function() {
        $('#agencySetModal').removeClass('hidden');
    });
    
    // 关闭设置模态窗
    $('#closeSetModal, #cancelSetUser').on('click', function() {
        $('#agencySetModal').addClass('hidden');
    });
    
    // 提交设置表单
    $('#agencySetForm').on('submit', function(e) {
        e.preventDefault();
        
        // 收集表单数据
        var formData = {
            allow_new_member: $('input[name="allow_new_member"]').is(':checked') ? 1 : 0,
            enable_review: $('input[name="enable_review"]').is(':checked') ? 1 : 0
        };
        
        // 发送AJAX请求
        $.ajax({
            url: '/Manage/saveSettings',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(res) {
                if (res.code == 200) {
                    _alert('设置保存成功');
                    $('#agencySetModal').addClass('hidden');
                    
                    // 刷新页面
                    window.location.reload();
                } else {
                    _alert(res.msg || '保存失败');
                }
            },
            error: function() {
                _alert('网络错误，请稍后重试');
            }
        });
    });
});
