<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{:Config('app_name')}} - 管理</title>
    {{include file="common/content"}}
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
        }

        .tab-active {
            color: #1e88e5;
            border-bottom: 2px solid #1e88e5;
        }

        .search-input:focus {
            outline: none;
        }

        .member-card {
            transition: all 0.3s ease;
        }

        .member-card:active {
            transform: scale(0.98);
        }

        .action-btn {
            transition: all 0.2s ease;
        }

        .action-btn:active {
            transform: scale(0.95);
        }
    </style>
</head>

<body class="bg-gray-100">
    <div class="pb-16">
        <!-- 顶部个人信息区 -->
        <div class="w-full bg-primary text-white">
            <div class="flex items-center px-4 py-4">
                <div class="w-16 h-16 rounded-full overflow-hidden border-2 border-white">
                    <img src="{{$userRow.avatar}}" alt="用户头像" class="w-full h-full object-cover">
                </div>
                <div class="ml-3 flex-1">
                    <h2 class="text-xl font-medium">{{:base64_decode($userRow['nickname'])}}</h2>
                    <p class="text-sm opacity-80">ID:{{$userRow.user_id}}</p>
                </div>
                <div class="flex flex-col space-y-2">
                    <!-- <button class="px-3 py-2 bg-white bg-opacity-20 rounded-button text-sm flex items-center">
                        <i class="ri-download-line mr-1"></i>
                        <span>导出列表</span>
                    </button> -->
                    <button class="px-3 py-2 bg-white bg-opacity-20 rounded-button text-sm flex items-center agency_set">
                        <i class="ri-settings-line"></i>
                        <span>设置</span>
                    </button>
                </div>
            </div>
        </div>
        <!-- 账户数据统计区 -->
        <div class="pt-4 px-4">
            <div class="grid grid-cols-2 gap-2">
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <p class="text-sm text-gray-500">账户余额</p>
                    <p class="text-xl text-primary font-medium mt-1">{{:number_format($userRow.balance,2)}}</p>
                    <!-- <button class="mt-2 text-xs text-primary px-2 py-1 bg-blue-50 rounded-full">充值</button> -->
                </div>
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <p class="text-sm text-gray-500">今日已发金额</p>
                    <p class="text-xl text-gray-700 font-medium mt-1">{{:number_format($userRow.today_amount,2)}}</p>
                </div>
                <!-- <div class="bg-white rounded-lg p-3 shadow-sm">
                    <p class="text-sm text-gray-500">订单统计</p>
                    <p class="text-xl text-gray-700 font-medium mt-1">0</p>
                </div> -->
            </div>
        </div>

        <!-- 会员管理状态标签页 -->
        <div class="mt-4 px-4">
            <div class="grid grid-cols-3 border-b">
                <a class="py-3 text-center {{if $filter['status'] == 9}} tab-active {{else /}} text-gray-500 {{/if}}" href="{{:url('manage/index',array('status'=>9))}}">
                    <button>群管理员({{$tubeApproveCount}})</button>
                </a>
                <a class="py-3 text-center {{if $filter['status'] == 1}} tab-active {{else /}} text-gray-500 {{/if}}" href="{{:url('manage/index',array('status'=>1))}}">
                    <button>待审群管({{$tubePendingCount}})</button>
                </a>
                <a class="py-3 text-center {{if $filter['status'] == 5}} tab-active {{else /}} text-gray-500 {{/if}}" href="{{:url('manage/index',array('status'=>5))}}">
                    <button>已拒绝({{$tubeRefuseCount}})</button>
                </a>
            </div>
        </div>

        <!-- 搜索框 -->
        <div class="px-4 pt-4">
            <form action="{{:url('manage/index')}}" method="get" id="search-form">
                <input type="hidden" name="status" value="{{$filter['status']}}">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="ri-search-line text-gray-400"></i>
                    </div>
                    <input type="text" class="search-input w-full pl-10 pr-4 py-3 bg-white rounded-lg text-sm border-none" name="keyword" value="{{$filter.keyword}}" placeholder="昵称、姓名">
                </div>
            </form>
        </div>

        <!-- 会员列表区域 -->
        <div class="px-4 mt-4" id="user-list-container">
            {{volist name="list" id="vo"}}
            <!-- 会员卡片1 -->
            <div class="bg-white rounded-lg shadow-sm p-4 mb-4 user-row">
                <div class="flex">
                    <div class="mr-3">
                        <img src="{{$vo.avatar}}" alt="会员头像" class="w-16 h-16 object-cover rounded-lg">
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center">
                            <h3 class="text-base font-medium">{{:base64_decode($vo['tube_name'])}}</h3>
                            <div class="w-5 h-5 flex items-center justify-center ml-1 text-primary edit-user-btn" data-user-id="{{$vo.wx_user_id}}" data-real-name="{{$vo.tube_name|default=''}}" data-mobile="{{$vo.tel|default=''}}">
                                <i class="ri-edit-box-line"></i>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">{{$vo.tube_id}}</p>
                        <div class="grid grid-cols-4 mt-3">
                            <div>
                                <p class="text-xs text-gray-500">今日观看</p>
                                <p class="text-sm text-primary mt-1">{{$vo.watch_count}} 人</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">今日完播</p>
                                <p class="text-sm text-primary mt-1">{{$vo.finish_count}} 人</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">完播率</p>
                                <p class="text-sm text-primary mt-1">{{$vo.finish_rate}} %</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">红包金额</p>
                                <p class="text-sm text-primary mt-1">{{:number_format($vo.red_amount,2)}} 元</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end mt-4 space-x-2">
                    <!-- <button class="text-sm text-gray-500">更多</button> -->
                    {{if $vo.status == 1}}
                        <button class="px-4 py-1.5 bg-green-500 text-white text-sm rounded-button">
                            <a href="{{:url('check',array('id'=>$vo['tube_id'],'status'=>9))}}">通过</a>
                        </button>
                        <button class="px-4 py-1.5 bg-red-500 text-white text-sm rounded-button">
                            <a href="{{:url('check',array('id'=>$vo['tube_id'],'status'=>5))}}">拒绝</a>
                        </button>
                    {{/if}}
                </div>
            </div>
            {{/volist}}
        </div>

        <!-- 加载状态 -->
        <div id="loading-status" class="text-center text-gray-500 py-6">
            <div id="loading" class="hidden">
                <div class="inline-block w-4 h-4 border-2 border-gray-300 border-t-primary rounded-full animate-spin mr-2"></div>
                <span>加载中...</span>
            </div>
            <div id="no-more" class="">没有更多了</div>
        </div>
    </div>
    <!-- 底部标签栏 -->
    {{include file="common/footer"}}

    <!-- 邀请按钮 -->
    <div class="fixed right-5 bottom-20 z-10">
        <button class="w-14 h-14 rounded-full bg-primary text-white shadow-lg flex items-center justify-center fn-copy" data-clipboard-text="{{$inviteUrl}}">
            <i class="ri-user-add-fill ri-xl"></i>
        </button>
    </div>

    <!-- 编辑用户信息模态窗口 -->
    <div id="editUserModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg w-11/12 max-w-md mx-auto">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium">编辑群管信息</h3>
                    <button id="closeEditModal" class="text-gray-500 hover:text-gray-700">
                        <i class="ri-close-line ri-lg"></i>
                    </button>
                </div>
            </div>
            <div class="p-4">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId" name="user_id" value="">
                    <div class="mb-4">
                        <label for="editUserName" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                        <input type="text" id="editUserName" name="real_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    <div class="mb-4">
                        <label for="editUserPhone" class="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                        <input type="text" id="editUserPhone" name="mobile" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    <div class="flex justify-end">
                        <button type="button" id="cancelEditUser" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md mr-2">取消</button>
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 设置模态窗 -->
    <div id="agencySetModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg w-11/12 max-w-md mx-auto">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium">权限设置</h3>
                    <button id="closeSetModal" class="text-gray-500 hover:text-gray-700">
                        <i class="ri-close-line ri-lg"></i>
                    </button>
                </div>
            </div>
            <div class="p-4">
                <form id="agencySetForm">
                    <input type="hidden" id="agencyId" name="agency_id" value="{{$userRow.agency_id|default=''}}">
                    
                    <!-- 开关选项 -->
                    <div class="space-y-4">
                        <!-- 允许新会员观看 -->
                        <div class="flex items-center justify-between py-3 border-b border-gray-100">
                            <div class="text-gray-700">是否允许新会员观看</div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" name="allow_new_member" value="1" class="sr-only peer" {{if $userRow.permit_new_user == 1}}checked{{/if}}>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                            </label>
                        </div>
                        
                        <!-- 开启待审核 -->
                        <div class="flex items-center justify-between py-3 border-b border-gray-100">
                            <div class="text-gray-700">是否开启待审核</div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" name="enable_review" value="1" class="sr-only peer" {{if $userRow.open_audit_user == 1}}checked{{/if}}>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                            </label>
                        </div>
                    </div>
                    <div class="flex justify-end mt-6">
                        <button type="button" id="cancelSetUser" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md mr-2">取消</button>
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

</body>

<script src="__PUBLIC__/js-build/manage.js" ></script>
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
<script src="__PUBLIC__/js/plugins/clipboard/clipboard.min.js?v=2.0"></script>

<script>
    $(function(){
        var clipboard = new ClipboardJS('.fn-copy');   
        clipboard.on('success', function(e) {  
            _alert("创建并复制链接成功");
        });  
        clipboard.on('error', function(e) {  
            _alert("创建失败");
        });
    });
    // 充值按钮点击
    $('.bg-blue-50.rounded-full').on('click', function () {
        const $rechargeModal = $('<div>', {
            'class': 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50',
            'html': `
                <div class="bg-white rounded-lg w-4/5 p-4">
                    <h3 class="text-lg font-medium mb-3">账户充值</h3>
                    <div class="mb-4">
                        <label class="block text-sm text-gray-600 mb-1">充值金额</label>
                        <input type="text" class="w-full border border-gray-300 rounded-lg px-3 py-2" placeholder="请输入充值金额">
                    </div>
                    <div class="flex space-x-3">
                        <button class="flex-1 py-2 bg-gray-200 rounded-button text-gray-600">取消</button>
                        <button class="flex-1 py-2 bg-primary text-white rounded-button">确认充值</button>
                    </div>
                </div>
            `
        });
        $('body').append($rechargeModal);

        $rechargeModal.find('.bg-gray-200').on('click', function () {
            $rechargeModal.remove();
        });

        $rechargeModal.find('.bg-primary').on('click', function () {
            const amount = $rechargeModal.find('input').val();
            if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
                if (!$rechargeModal.find('.text-red-500').length) {
                    $('<p>', {
                        'class': 'text-red-500 text-sm mt-2',
                        'text': '请输入有效的充值金额'
                    }).appendTo($rechargeModal.find('.mb-4'));
                }
                return;
            }
            $rechargeModal.remove();

            // 显示充值成功提示
            const $successToast = $('<div>', {
                'class': 'fixed top-16 left-1/2 transform -translate-x-1/2 bg-primary text-white px-4 py-2 rounded-lg z-50',
                'text': '充值申请已提交'
            });
            $('body').append($successToast);

            setTimeout(function() {
                $successToast.remove();
            }, 2000);
        });
    });

    // 更多按钮点击
    $('.text-sm.text-gray-500').on('click', function () {
        const $memberCard = $(this).closest('.bg-white.rounded-lg');
        const memberName = $memberCard.find('h3').text();

        const $actionSheet = $('<div>', {
            'class': 'fixed inset-0 bg-black bg-opacity-50 flex flex-col justify-end z-50',
            'html': `
                <div class="bg-white rounded-t-xl p-4">
                    <h3 class="text-center text-lg font-medium mb-4">${memberName}</h3>
                    <div class="grid grid-cols-4 gap-4 mb-4">
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-message-2-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">发消息</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-price-tag-3-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">设标签</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-red-packet-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">发红包</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-file-list-3-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">详情</span>
                        </div>
                    </div>
                    <button class="w-full py-3 bg-gray-100 rounded-lg text-base">取消</button>
                </div>
            `
        });
        $('body').append($actionSheet);

        $actionSheet.find('.bg-gray-100').on('click', function () {
            $actionSheet.remove();
        });

        // 点击背景关闭
        $actionSheet.on('click', function (e) {
            if (e.target === this) {
                $actionSheet.remove();
            }
        });
    });


    // 更换会员归属按钮点击
    $('.bg-red-50.text-secondary').on('click', function () {
        const $memberCard = $(this).closest('.bg-white.rounded-lg');
        const memberName = $memberCard.find('h3').text();

        const $modal = $('<div>', {
            'class': 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50',
            'html': `
                <div class="bg-white rounded-lg w-4/5 p-4">
                    <h3 class="text-lg font-medium mb-3">更换会员归属</h3>
                    <p class="text-sm text-gray-600 mb-3">将 "${memberName}" 的归属更换为：</p>
                    <div class="mb-4">
                        <div class="flex items-center p-3 border border-gray-200 rounded-lg mb-2">
                            <input type="radio" name="owner" id="owner1" checked>
                            <label for="owner1" class="ml-2 text-sm">王文超</label>
                        </div>
                        <div class="flex items-center p-3 border border-gray-200 rounded-lg">
                            <input type="radio" name="owner" id="owner2">
                            <label for="owner2" class="ml-2 text-sm">其他管理员</label>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <button class="flex-1 py-2 bg-gray-200 rounded-button text-gray-600">取消</button>
                        <button class="flex-1 py-2 bg-primary text-white rounded-button">确认更换</button>
                    </div>
                </div>
            `
        });
        $('body').append($modal);

        $modal.find('.bg-gray-200').on('click', function () {
            $modal.remove();
        });

        $modal.find('.bg-primary').on('click', function () {
            $modal.remove();

            // 显示更换成功提示
            const $successToast = $('<div>', {
                'class': 'fixed top-16 left-1/2 transform -translate-x-1/2 bg-primary text-white px-4 py-2 rounded-lg z-50',
                'text': '归属更换成功'
            });
            $('body').append($successToast);

            setTimeout(function() {
                $successToast.remove();
            }, 2000);
        });
    });

    // 邀请按钮点击
    /*
    $('.rounded-full.bg-primary').on('click', function () {
        const $actionSheet = $('<div>', {
            'class': 'fixed inset-0 bg-black bg-opacity-50 flex flex-col justify-end z-50',
            'html': `
                <div class="bg-white rounded-t-xl p-4">
                    <h3 class="text-center text-lg font-medium mb-4">邀请新会员</h3>
                    <div class="grid grid-cols-4 gap-4 mb-4">
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-wechat-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">微信好友</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-group-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">微信群</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-qr-code-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">二维码</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-link ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">链接</span>
                        </div>
                    </div>
                    <button class="w-full py-3 bg-gray-100 rounded-lg text-base">取消</button>
                </div>
            `
        });
        $('body').append($actionSheet);

        $actionSheet.find('.bg-gray-100').on('click', function () {
            $actionSheet.remove();
        });

        // 点击背景关闭
        $actionSheet.on('click', function (e) {
            if (e.target === this) {
                $actionSheet.remove();
            }
        });
    });
    */
</script>

</html>
