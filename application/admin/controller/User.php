<?php

namespace app\admin\controller;

use think\facade\Request;
use app\admin\model\User as UserModel;
use think\Db;

class User extends Base
{
    protected function initialize()
    {
        parent::initialize();

        // 设置当前标签
        $this->assign('navtab', 'user');
    }

    public function index()
    {
        // 验证当前登录用户类型，是否是群管或经销商，如果不是，直接阻止
        if (!in_array($this->accountInfo['type'], [1, 2])) {
            return redirect('error/index')->with('error_msg', '您没有权限访问');
        }

        $filter['status'] = Request::param('status', 9);
        $filter['keyword'] = Request::param('keyword', '');

        // 获取群管信息
        $userRow = $this->accountInfo;
        if (empty($userRow)) {
            return redirect('error/index')->with('error_msg', '群管信息不存在');
        }

        $User = new UserModel();
        // 获取会员列表
        $userRows = $User->getUserList($this->openid, $filter, 1);

        //邀请链接
        if ($this->accountInfo['type'] == 1) {
            $inviteUrl = config('group_mgr_url') . '?state=' . $this->accountInfo['wxgzh_id'] . '_' . $this->accountInfo['user_id'];
        } else {
            $inviteUrl = config('member_url') . '?state=' . $this->accountInfo['wxgzh_id'] . '_' . $this->accountInfo['user_id'];
        }

        // 模板赋值
        $this->assign([
            'userRow' => $userRow,
            'list' => $userRows['list'],
            'page' => $userRows['page'],
            'memberCount' => $userRows['member'],
            'pendingCount' => $userRows['pending'],
            'refuseCount' => $userRows['refuse'],
            'darkCount' => $userRows['dark'],
            'filter' => $filter,
            'inviteUrl' => $inviteUrl
        ]);

        return $this->fetch();
    }

    /**
     * 加载更多会员数据
     *
     * @return \think\response\Json
     */
    public function loadMore()
    {
        $filter['status'] = Request::param('status', 9);
        $filter['keyword'] = Request::param('keyword', '');
        $page = Request::param('page', 1, 'intval');

        $User = new UserModel();

        // 获取会员列表
        $userList = $User->getUserList($this->openid, $filter, $page);

        // 将分页对象转换为数组
        $listData = [];
        if ($userList['list'] && $userList['list']->count() > 0) {
            $listData = $userList['list']->toArray();
            $listData = $listData['data'];
        }

        return json([
            'code' => 200,
            'data' => $listData,
            'has_more' => $userList['has_more']
        ]);
    }

    /**
     * 审核会员
     *
     * @return void
     */
    public function check()
    {
        $id = Request::param('id', 0);
        $status = Request::param('status', 0);
        if ($id == 0 || $status == 0) {
            return $this->error('参数错误');
        }
        $User = new UserModel();
        $result = $User->checkUser($id, $status);
        if ($result['code'] != 200) {
            return $this->error($result['msg'], url('index'));
        }else{
            return $this->success($result['msg'], url('index'));
        }
    }

    public function info()
    {
        $type = Request::param('type', 1);
        $this->assign('type', $type);
        if ($this->accountInfo['type'] != 3) {
            return redirect('error/index')->with('error_msg', '您没有权限访问');
        }
        $accountInfo = $this->accountInfo;
        $today = date('Y-m-d');
        $todaytime = strtotime($today);
        switch ($type) {
            case 1:
                $result = $this->calculateBatchStatistics($todaytime, $todaytime+86399);
                break;
            case 2:
                //获取本周开始日期、结束日期
                $monday = date('Y-m-d', strtotime('monday this week'));
                $sunday = date('Y-m-d', strtotime('sunday this week'));
                $result = $this->calculateBatchStatistics(strtotime($monday), strtotime($sunday)+86399);
                break;
            case 3:
                //获取本月开始日期、结束日期
                $month_start = date('Y-m-d', strtotime('first day of this month'));
                $month_end = date('Y-m-d', strtotime('last day of this month'));
                $result = $this->calculateBatchStatistics(strtotime($month_start), strtotime($month_end)+86399);
                break;
        }
        //查询items
        $items = [];
        if(!empty($result['items'])){
            $where = [];
            $where[] = ['p.status', '=', 1];
            $where[] = ['p.del', '=', 0];
            $where[] = ['g.status', '=', 1];
            $where[] = ['g.del', '=', 0];
            $where[] = ['i.del', '=', 0];
            $where[] = ['i.status', '=', 1];
            $where[] = ['i.item_id', 'in', $result['items']];
            $items =  Db::name('org_period_item i')
                    ->leftJoin('org_period p', 'i.period_id = p.period_id')
                    ->leftjoin('org_period_group g', 'g.group_id = p.group_id')
                    ->leftJoin('org_course c', 'c.course_id = i.course_id')
                    ->leftJoin('org_video v', 'v.video_id = c.video_id')
                    ->field('g.group_name,p.period_name,i.item_name,i.start_time,i.end_time,c.cover_url,v.oss_video_id,v.duration,i.item_id')
                    ->where($where)
                    ->select();
        }
        // dump($items);
        //今日观看数据
        $this->assign("userRow", $this->accountInfo);
        $this->assign("result", $result);
        $this->assign("items", $items);
        return $this->fetch();
    }

    /**
     * 更新会员信息
     *
     * @return \think\response\Json
     */
    public function updateUserInfo()
    {
        if (!Request::isPost()) {
            return json(['code' => 400, 'msg' => '请求方式错误']);
        }

        $userId = Request::post('user_id', 0, 'intval');
        $realName = Request::post('real_name', '', 'trim');
        $mobile = Request::post('mobile', '', 'trim');

        if (empty($userId)) {
            return json(['code' => 400, 'msg' => '会员ID不能为空']);
        }

        if (empty($realName)) {
            return json(['code' => 400, 'msg' => '会员姓名不能为空']);
        }

        // 手机号格式验证
        if (!empty($mobile) && !preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            return json(['code' => 400, 'msg' => '手机号格式不正确']);
        }

        try {
            // 更新会员信息
            $updateData = [
                'user_name' => base64_encode($realName),
                'update_time' => time()
            ];

            if (!empty($mobile)) {
                $updateData['tel'] = $mobile;
            }

            $result = Db::name('org_wxuser')->where('user_id', $userId)->update($updateData);

            if ($result !== false) {
                return json(['code' => 200, 'msg' => '更新成功']);
            } else {
                return json(['code' => 500, 'msg' => '更新失败']);
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }

    /**
     * 批量计算统计数据
     */
    private function calculateBatchStatistics($startTime, $endTime)
    {
        $user_id = $this->accountInfo['user_id'];
        // 构建观看人数的查询条件（使用create_time）
        $watchWhere = [
            ['create_time', 'between', [$startTime, $endTime]],
        ];

        // 构建完播、答题、答对人数的查询条件（使用end_play_time）
        $finishWhere = [
            ['end_play_time', 'between', [$startTime, $endTime]]
        ];

        // 查询观看人数
        $watchStats = Db::name('org_user_watch')
            ->where($watchWhere)
            ->where('user_id',$user_id)
            ->field('
                COUNT(item_id) as watch_count,
                GROUP_CONCAT(item_id) as items
            ')
            ->find();
        // 查询完播、答题、答对人数
        $finishStats = Db::name('org_user_watch')
            ->where($finishWhere)
            ->where('user_id',$user_id)
            ->field('
                COUNT(DISTINCT CASE WHEN is_finish = 1 THEN user_id ELSE NULL END) as finish_count,
                COUNT(DISTINCT CASE WHEN answer_id > 0 THEN user_id ELSE NULL END) as answer_count,
                COUNT(DISTINCT CASE WHEN is_correct = 1 THEN user_id ELSE NULL END) as correct_count
            ')
            ->find();
        
        // 合并两个查询结果
        $statistics = [
            'watch_count' => $watchStats['watch_count'],
            'items' => $watchStats['items'],
            'finish_count' => $finishStats['finish_count'],
            'answer_count' => $finishStats['answer_count'],
            'correct_count' => $finishStats['correct_count'],
        ];
        
        // 转换为索引数组返回
        return $statistics;
    }
}
