<?php

namespace app\admin\model;

use think\Model;
use think\Db;

class User extends Model
{
    /**
     * 获取会员列表
     * 
     * @param int $openid 
     * @param array $filter 筛选条件
     * @param int $page 页码
     * @return array
     */
    public function getUserList($openid, $filter, $page = 1)
    {
        $status = $filter['status'] ?? 9;
        $loginRow = Db::name('org_wxuser')->where(['openid' => $openid])->find();

        $type = $loginRow['type'];

        $configs = [
            1 => [
                'subfield' => 'u.agency_id',
                'likefield' => 'a.agency_name',
                'countfield' => 'agency_id'
            ],
            2 => [
                'subfield' => 'u.tube_id',
                'likefield' => 't.tube_name',
                'countfield' => 'tube_id'
            ]
        ];

        switch ($type) {
            case '1':
                $subid = Db::name('org_agency')->where(['wx_user_id' => $loginRow['user_id']])->value('agency_id');
                break;
            case '2':
                $subid = Db::name('org_agency_tube')->where(['wx_user_id' => $loginRow['user_id']])->value('tube_id');
                break;
        }

        // 构建查询条件
        $condition = [];
        $condition[] = [$configs[$type]['subfield'], '=', $subid];
        $condition[] = ['u.type', '=', 3];
        $condition[] = ['u.status', '=', $status];

        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $likefield = $configs[$type]['likefield'];
            $base64Keyword = base64_encode($filter['keyword']);
            $condition[] = ["u.nickname|u.user_name|$likefield", 'like', "%$base64Keyword%"];
        }

        //获取用户今天是否watch
        $today = date('Y-m-d');
        $todaytime = strtotime($today);

        $list = Db::name('org_wxuser u')
            ->field("u.*, t.tube_name")
            ->join('ksd_org_agency_tube t', 'u.tube_id = t.tube_id', 'left')
            ->where($condition)
            ->order('u.create_time desc')
            ->paginate(10, false, ['page' => $page])
            ->each(function ($item, $key) use ($todaytime) {
                $t_watch = Db::name('org_user_watch')
                    ->where([
                        ['user_id', '=', $item['user_id']],
                        ['create_time', '>=', $todaytime]
                    ])
                    ->count();
                //获取用户参与总item数
                $p_count = Db::name('org_user_watch')->where('user_id', $item['user_id'])->group('item_id')->count();
                $item['t_watch'] = $t_watch ?? 0;
                $item['p_count'] = $p_count ?? 0;
                return $item;
            });

        // 统计会员数量
        $countMap = [
            'type' => 3,
            $configs[$type]['countfield'] => $subid
        ];
        //审核通过的数量
        $member = Db::name('org_wxuser')->where($countMap)->where(['status' => 9])->count();
        //待审核数量
        $pending = Db::name('org_wxuser')->where($countMap)->where(['status' => 1])->count();
        //已拒绝数量
        $refuse = Db::name('org_wxuser')->where($countMap)->where(['status' => 5])->count();
        //小黑屋数量
        $dark = Db::name('org_wxuser')->where($countMap)->where(['status' => 4])->count();

        $has_more = $list->total() > $page * 10;

        return [
            'list' => $list,
            //'page' => $list->render(),
            'member' => $member,
            'pending' => $pending,
            'refuse' => $refuse,
            'dark' => $dark,
            'has_more' => $has_more
        ];
    }

    /**
     * 审核会员
     *
     * @param [type] $id
     * @param [type] $status
     * @return void
     */
    public function checkUser($id, $status)
    {
        $row = Db::name('org_wxuser')->where(['user_id' => $id])->find();
        if (empty($row)) {
            return ['code' => 400, 'msg' => '用户信息不存在'];
        }

        $data = [
            'status' => $status,
            'update_time' => time(),
        ];
        $result = Db::name('org_wxuser')->where(['user_id' => $id])->update($data);
        if ($result === false) {
            return ['code' => 400, 'msg' => '操作失败'];
        }
        return ['code' => 200, 'msg' => '操作成功'];
    }
}
